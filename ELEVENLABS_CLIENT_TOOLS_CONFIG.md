# ElevenLabs Client Tools Configuration for Nathan Agent

This document explains how to configure client tools in the ElevenLabs dashboard for the <PERSON> fitness coach agent to enable hands-free workout control.

## Required Client Tools

Configure these client tools in your ElevenLabs agent dashboard:

### 1. count_reps
**Purpose**: Track repetition counts during exercises
**Type**: Client Tool
**Parameters**:
- `amount` (number): The number of reps to set

**Example Usage**: "I did 12 reps" → Sets current reps to 12

### 2. complete_set
**Purpose**: Mark current set as complete and move to rest
**Type**: Client Tool
**Parameters**: None

**Example Usage**: "Set complete" → Completes current set with tracked reps/weight

### 3. start_rest
**Purpose**: Initiate rest period between sets
**Type**: Client Tool
**Parameters**:
- `duration` (number, optional): Rest duration in seconds (default: 60)

**Example Usage**: "Start rest for 90 seconds" → Starts 90s rest timer

### 4. skip_rest
**Purpose**: Skip current rest period
**Type**: Client Tool
**Parameters**: None

**Example Usage**: "Skip rest" → Ends rest period early

### 5. start_exercise
**Purpose**: Navigate to a specific exercise by name
**Type**: Client Tool
**Parameters**:
- `name` (string): Exercise name or partial match

**Example Usage**: "Go to bench press" → Navigates to bench press exercise

### 6. next_exercise
**Purpose**: Move to the next exercise in workout
**Type**: Client Tool
**Parameters**: None

**Example Usage**: "Next exercise" → Advances to next exercise

### 7. end_workout
**Purpose**: Complete the entire workout session
**Type**: Client Tool
**Parameters**: None

**Example Usage**: "End workout" → Completes workout and shows summary

## Configuration Steps

1. Go to https://elevenlabs.io/app/conversational-ai
2. Select your Nathan agent (agent_01jx195padeb0spkjn54zt3ah0)
3. Navigate to "Tools" section
4. For each tool above:
   - Click "Add Tool"
   - Set Type to "Client"
   - Enter the tool name exactly as shown
   - Add parameters with correct types
   - Set "Wait for response" to OFF (these are fire-and-forget)
   - Add clear descriptions for the AI to understand when to use them

## Testing Voice Commands

Once configured, test these voice commands during a workout:

- "I did 15 reps" → Updates rep count
- "Set complete" → Completes current set
- "Start my rest" → Begins rest period
- "Skip rest, I'm ready" → Skips rest
- "Go to squats" → Jumps to squats exercise
- "Next exercise please" → Moves to next exercise
- "I'm done with this workout" → Ends workout

## Visual Feedback

The app provides visual feedback for voice states:
- **Green ring**: Connected and ready
- **Blue ring**: User is speaking
- **Orange ring**: Nathan is listening
- **Yellow ring**: Connecting
- **Red ring**: Error state

The avatar icon changes from brain (🧠) to microphone (🎤) when voice is active.

## Troubleshooting

1. **Tools not responding**: Ensure tool names match exactly in dashboard
2. **Voice not activating**: Check .env file has correct API key and agent ID
3. **Echo issues**: App implements echo cancellation, but use headphones for best results
4. **Commands not recognized**: Speak clearly and use the example phrases above

## Advanced Configuration

For better recognition, add these to Nathan's system prompt:

```
You have access to workout control tools:
- count_reps: Update rep count when user mentions numbers
- complete_set: Mark set done when user says "complete", "done", "finished"
- start_rest: Begin rest when user wants to rest
- skip_rest: Skip rest when user is ready to continue
- start_exercise: Navigate to specific exercises by name
- next_exercise: Move to the next exercise
- end_workout: End the entire workout session

Always confirm actions with brief responses like "Set complete, rest for 60 seconds?" or "Moving to bench press".
```