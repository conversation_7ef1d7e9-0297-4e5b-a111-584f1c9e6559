# Hands-Free Mode Implementation Guide

## Problem
<PERSON> is not correctly using the dynamic variables sent by the app, causing confusion during workouts.

## Recommended Approach

### Step 1: Try Dynamic Variables Fix First
1. Clear test values in ElevenLabs dashboard
2. Update <PERSON>'s system prompt to be more explicit
3. Test again with fresh session

### Step 2: If Dynamic Variables Still Fail, Implement Mode Selection

Add a mode selection dialog when starting workout:

```dart
// In workout_loading_screen.dart or pre_workout_screen.dart
void _showModeSelectionDialog() {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: AppColorPalette.darkCard,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: Column(
        children: [
          Icon(
            Icons.fitness_center,
            color: AppColorPalette.primaryOrange,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Choose Workout Mode',
            style: AppTypography.h2.copyWith(color: Colors.white),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Regular Mode
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
              _navigateToRegularWorkout();
            },
            child: Container(
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.touch_app, color: Colors.white, size: 32),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Regular Mode',
                          style: AppTypography.h3.copyWith(color: Colors.white),
                        ),
                        Text(
                          'Full UI with manual controls',
                          style: AppTypography.body2.copyWith(color: Colors.white60),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Hands-Free Mode
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
              _navigateToHandsFreeWorkout();
            },
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColorPalette.primaryOrange,
                    AppColorPalette.primaryOrangeLight,
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(Icons.mic, color: Colors.white, size: 32),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Hands-Free Mode',
                          style: AppTypography.h3.copyWith(color: Colors.white),
                        ),
                        Text(
                          'Voice-controlled with Nathan',
                          style: AppTypography.body2.copyWith(color: Colors.white.withOpacity(0.9)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
```

## Benefits of Hands-Free Mode

1. **Simplified UI** - Large, clear displays perfect for voice control
2. **Explicit Context** - Send workout info as messages rather than relying on dynamic variables
3. **Better UX** - Designed specifically for voice interaction
4. **Fallback Option** - Users can switch between modes based on preference

## Alternative: Fix in Voice Service

Instead of dynamic variables, send explicit context after connection:

```dart
// In voice_workout_service.dart
Future<void> startVoiceWorkout(WorkoutSession workout) async {
  _currentWorkout = workout;
  
  try {
    // Start basic conversation without dynamic variables
    await _voiceService.startConversation();
    
    // Wait for connection
    await Future.delayed(Duration(seconds: 2));
    
    // Send explicit context as first message
    final context = "I'm doing ${workout.name}. Currently on ${workout.currentExercise?.name}, set ${workout.currentSetIndex + 1}.";
    
    // This would need a method to send user messages programmatically
    // await _voiceService.sendUserMessage(context);
    
    _isVoiceActive = true;
    notifyListeners();
  } catch (e) {
    debugPrint('Error starting voice workout: $e');
    rethrow;
  }
}
```

## Recommendation

1. First, try fixing the dynamic variables issue in ElevenLabs
2. If that fails, implement the mode selection with hands-free option
3. The hands-free mode provides a better voice-first experience anyway