# Nathan Agent Client Tools Setup Guide

Based on your ElevenLabs dashboard, here's how to add the required client tools:

## Steps to Add Client Tools

1. In your Nathan agent dashboard, click on "Tools" in the left sidebar
2. Click "Add Tool" or "Create Tool"
3. For each tool below, configure as follows:

## Required Client Tools Configuration

### 1. count_reps
- **Name**: count_reps
- **Type**: Client
- **Description**: Updates the repetition count for the current exercise set
- **Parameters**:
  - `amount` (number, required): The number of repetitions completed

### 2. complete_set
- **Name**: complete_set  
- **Type**: Client
- **Description**: Marks the current set as complete and moves to rest period
- **Parameters**: None

### 3. start_rest
- **Name**: start_rest
- **Type**: Client  
- **Description**: Starts a rest timer between sets
- **Parameters**:
  - `duration` (number, optional): Rest duration in seconds (default: 60)

### 4. skip_rest
- **Name**: skip_rest
- **Type**: Client
- **Description**: Skips the current rest period and continues workout
- **Parameters**: None

### 5. start_exercise
- **Name**: start_exercise
- **Type**: Client
- **Description**: Navigates to a specific exercise by name
- **Parameters**:
  - `name` (string, required): The name of the exercise to start

### 6. next_exercise
- **Name**: next_exercise
- **Type**: Client
- **Description**: Moves to the next exercise in the workout
- **Parameters**: None

### 7. end_workout
- **Name**: end_workout
- **Type**: Client
- **Description**: Completes the entire workout session
- **Parameters**: None

## System Prompt Update

Also update Nathan's system prompt to include:

```
You are Nathan, an AI fitness coach helping users during their workouts. When users tell you about their current workout (e.g., "I'm doing Back Workout. Currently on deadlifts, set 1 of 3. Target is 8 reps."), acknowledge their current exercise and help them track progress.

You have access to these tools to control their workout:
- count_reps: Update rep count when they mention numbers
- complete_set: Mark set complete when they say "done", "complete", "finished"  
- start_rest: Begin rest timer after completing a set
- skip_rest: Skip rest when they're ready to continue
- start_exercise: Jump to a specific exercise by name
- next_exercise: Move to the next exercise
- end_workout: Finish the workout session

Always be encouraging and provide brief, actionable responses. Focus on helping them complete their current exercise rather than asking what workout they want to do.
```

## Testing After Setup

1. Save all tools
2. Test in the playground with: "I'm doing Back Workout. Currently on deadlifts, set 1 of 3. Target is 8 reps."
3. Nathan should acknowledge the exercise and be ready to track
4. Try: "I did 8 reps" - should trigger count_reps tool
5. Try: "Set complete" - should trigger complete_set tool

The tools will automatically sync with your Flutter app once configured!