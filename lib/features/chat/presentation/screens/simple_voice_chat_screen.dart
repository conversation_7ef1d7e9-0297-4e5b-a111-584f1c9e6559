import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/services/simple_elevenlabs_service.dart';
import '../../../../shared/models/simple_conversation_models.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../core/theme/color_palette.dart';

class SimpleVoiceChatScreen extends StatefulWidget {
  const SimpleVoiceChatScreen({super.key});

  @override
  State<SimpleVoiceChatScreen> createState() => _SimpleVoiceChatScreenState();
}

class _SimpleVoiceChatScreenState extends State<SimpleVoiceChatScreen> 
    with TickerProviderStateMixin {
  final SimpleElevenLabsService _service = SimpleElevenLabsService();
  int _counter = 0;
  late AnimationController _counterAnimationController;
  late Animation<double> _counterScaleAnimation;

  @override
  void initState() {
    super.initState();
    _service.addListener(_updateUI);
    _setupClientTools();
    
    // Initialize counter animation
    _counterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _counterScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _counterAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _setupClientTools() {
    // Set up the client tool callback for counter control
    _service.onClientToolCall = (String toolName, Map<String, dynamic> parameters) {
      debugPrint('🎯 Received client tool call: $toolName with parameters: $parameters');

      if (toolName == 'counter_control') {
        final action = parameters['action'] as String?;
        // Handle both int and double values from ElevenLabs
        final value = parameters['value'] != null 
            ? (parameters['value'] is int 
                ? parameters['value'] as int 
                : (parameters['value'] as double).toInt())
            : null;
        final amount = parameters['amount'] != null 
            ? (parameters['amount'] is int 
                ? parameters['amount'] as int 
                : (parameters['amount'] as double).toInt())
            : 1;

        switch (action) {
          case 'increment':
            for (int i = 0; i < amount; i++) {
              _incrementCounter();
            }
            break;
          case 'decrement':
            for (int i = 0; i < amount; i++) {
              _decrementCounter();
            }
            break;
          case 'set':
            if (value != null) {
              _setCounter(value);
            }
            break;
          case 'reset':
            _resetCounter();
            break;
        }
      }
    };

    debugPrint('✅ Client tools configured for counter control');
  }

  @override
  void dispose() {
    _service.removeListener(_updateUI);
    _service.dispose();
    _counterAnimationController.dispose();
    super.dispose();
  }

  void _updateUI() {
    if (mounted) {
      setState(() {});
    }
  }

  void _incrementCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      _counter++;
    });
    _animateCounter();
  }

  void _decrementCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      if (_counter > 0) {
        _counter--;
      }
    });
    _animateCounter();
  }

  void _setCounter(int value) {
    HapticFeedback.lightImpact();
    setState(() {
      _counter = value.clamp(0, 9999); // Reasonable upper limit
    });
    _animateCounter();
  }

  void _resetCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      _counter = 0;
    });
    _animateCounter();
  }

  void _animateCounter() {
    _counterAnimationController.forward().then((_) {
      _counterAnimationController.reverse();
    });
  }

  Widget _buildCounterSection() {
    return GlassMorphismCard(
      padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Counter',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Decrement button
                GestureDetector(
                  onTap: _decrementCounter,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.remove,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

                // Counter display with animation
                AnimatedBuilder(
                  animation: _counterScaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _counterScaleAnimation.value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.1),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '$_counter',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                // Increment button
                GestureDetector(
                  onTap: _incrementCounter,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final service = _service;
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Voice Chat'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Status indicator
            Expanded(
              flex: 3,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated avatar
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: _getStatusColors(service.status),
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColors(service.status).first.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        _getStatusIcon(service.status),
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // Status text
                    Text(
                      _getStatusText(service.status),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Last transcript
                    if (service.lastTranscript.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'You said:',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              service.lastTranscript,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    
                    // Last response
                    if (service.lastResponse.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Assistant:',
                              style: TextStyle(
                                color: Colors.orange,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              service.lastResponse,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    // Voice command hints
                    if (service.status == SimpleConversationStatus.connected) ...[
                      const SizedBox(height: 20),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              '💡 Try saying:',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              '"Set counter to 10"\n"Add 5 reps"\n"Reset counter"',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Counter section - Fixed bottom section
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: _buildCounterSection(),
            ),

            // Control button
            Padding(
              padding: const EdgeInsets.fromLTRB(32, 16, 32, 20),
              child: SizedBox(
                width: 200,
                height: 60,
                child: ElevatedButton(
                  onPressed: () async {
                    if (service.status == SimpleConversationStatus.disconnected) {
                      await service.startConversation();
                    } else {
                      await service.stopConversation();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: service.status == SimpleConversationStatus.disconnected
                        ? Colors.orange
                        : Colors.red,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    service.status == SimpleConversationStatus.disconnected
                        ? 'Start Conversation'
                        : 'Stop Conversation',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  List<Color> _getStatusColors(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return [Colors.grey, Colors.grey.shade700];
      case SimpleConversationStatus.connecting:
        return [Colors.yellow, Colors.orange];
      case SimpleConversationStatus.connected:
        return [Colors.green, Colors.green.shade700];
      case SimpleConversationStatus.speaking:
        return [Colors.blue, Colors.blue.shade700];
      case SimpleConversationStatus.listening:
        return [Colors.purple, Colors.purple.shade700];
      case SimpleConversationStatus.error:
        return [Colors.red, Colors.red.shade700];
    }
  }
  
  IconData _getStatusIcon(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return Icons.power_settings_new;
      case SimpleConversationStatus.connecting:
        return Icons.sync;
      case SimpleConversationStatus.connected:
      case SimpleConversationStatus.listening:
        return Icons.hearing;
      case SimpleConversationStatus.speaking:
        return Icons.mic;
      case SimpleConversationStatus.error:
        return Icons.error_outline;
    }
  }
  
  String _getStatusText(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return 'Ready to chat with Nathan';
      case SimpleConversationStatus.connecting:
        return 'Connecting to Nathan...';
      case SimpleConversationStatus.connected:
        return '🎤 Listening for your voice';
      case SimpleConversationStatus.speaking:
        return '💭 Processing your request';
      case SimpleConversationStatus.listening:
        return '🗣️ Nathan is speaking';
      case SimpleConversationStatus.error:
        return '⚠️ Connection error';
    }
  }
}