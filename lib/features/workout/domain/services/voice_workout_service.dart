import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/simple_elevenlabs_service.dart';
import '../providers/workout_session_provider.dart';
import '../models/workout_session.dart';

class VoiceWorkoutService extends ChangeNotifier {
  final SimpleElevenLabsService _voiceService;
  final WorkoutSessionNotifier _workoutNotifier;
  final Ref _ref;
  
  bool _isVoiceActive = false;
  WorkoutSession? _currentWorkout;
  
  VoiceWorkoutService(this._voiceService, this._workoutNotifier, this._ref) {
    _voiceService.addListener(_onVoiceServiceUpdate);
    _setupClientTools();
  }

  bool get isVoiceActive => _isVoiceActive;
  get voiceStatus => _voiceService.status;
  
  /// Public access to voice service for direct conversation control
  SimpleElevenLabsService get voiceService => _voiceService;
  
  @override
  void dispose() {
    _voiceService.removeListener(_onVoiceServiceUpdate);
    super.dispose();
  }

  void _onVoiceServiceUpdate() {
    notifyListeners();
  }

  /// Start voice-controlled workout mode
  Future<void> startVoiceWorkout(WorkoutSession workout) async {
    _currentWorkout = workout;
    
    // Also sync with the current workout from session provider
    final sessionState = _ref.read(workoutSessionProvider);
    final sessionWorkout = sessionState.currentWorkout;
    if (sessionWorkout != null) {
      _currentWorkout = sessionWorkout;
      debugPrint('🔄 Synced with session workout: ${sessionWorkout.name}');
    }
    
    // Generate dynamic variables for the workout
    final currentExercise = _currentWorkout!.currentExercise;
    final currentSetNum = _currentWorkout!.currentSetIndex + 1;
    
    final customVariables = {
      'workout_name': _currentWorkout!.name,
      'current_exercise': currentExercise?.name ?? 'Unknown',
      'current_set': currentSetNum.toString(),
      'total_sets': currentExercise?.sets.toString() ?? '0',
      'target_reps': (currentExercise != null && currentExercise.targetReps.isNotEmpty) 
          ? currentExercise.targetReps[_currentWorkout!.currentSetIndex].toString()
          : '10',
      'exercise_number': '${_currentWorkout!.currentExerciseIndex + 1}',
      'total_exercises': _currentWorkout!.exercises.length.toString(),
    };
    
    debugPrint('🎤 Starting voice workout with dynamic variables: $customVariables');
    
    try {
      // Generate context message with workout details
      final contextMessage = '''
I'm starting my ${_currentWorkout!.name} workout. 
Currently on ${currentExercise?.name ?? 'first exercise'}, set $currentSetNum of ${currentExercise?.sets ?? 0}.
Target is ${currentExercise?.targetReps[_currentWorkout!.currentSetIndex] ?? 10} reps.
This is exercise ${_currentWorkout!.currentExerciseIndex + 1} of ${_currentWorkout!.exercises.length}.
      '''.trim();
      
      // Start conversation with both dynamic variables AND initial context
      await _voiceService.startConversation(
        customVariables: customVariables,
        initialContext: contextMessage,
      );
      _isVoiceActive = true;
      notifyListeners();
      
      debugPrint('🎯 Voice workout activated for: ${_currentWorkout!.name}');
      debugPrint('📝 Initial context sent: $contextMessage');
    } catch (e) {
      debugPrint('❌ Error starting voice workout: $e');
      rethrow;
    }
  }

  /// Stop voice-controlled workout mode
  Future<void> stopVoiceWorkout() async {
    try {
      await _voiceService.stopConversation();
      _isVoiceActive = false;
      _currentWorkout = null;
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error stopping voice workout: $e');
    }
  }

  /// Generate workout context string for Nathan
  String _generateWorkoutContext(WorkoutSession workout) {
    final currentExercise = workout.currentExercise;
    final currentSetNum = workout.currentSetIndex + 1;
    
    if (currentExercise != null) {
      final targetReps = currentExercise.targetReps.isNotEmpty 
          ? currentExercise.targetReps[workout.currentSetIndex].toString()
          : '?';
      
      return '''I'm doing ${workout.name}. Currently on ${currentExercise.name}, set $currentSetNum of ${currentExercise.sets}. Target is $targetReps reps.''';
    } else {
      return '''I'm doing ${workout.name} workout. Help me track my exercises.''';
    }
  }

  /// Setup client tools for Nathan to control the workout
  void _setupClientTools() {
    _voiceService.onClientToolCall = (String toolName, Map<String, dynamic> parameters) {
      debugPrint('🔧 Voice workout tool called: $toolName with $parameters');
      
      switch (toolName) {
        case 'start_exercise':
          _handleStartExercise(parameters);
          break;
        case 'count_reps':
          _handleCountReps(parameters);
          break;
        case 'complete_set':
          _handleCompleteSet(parameters);
          break;
        case 'start_rest':
          _handleStartRest(parameters);
          break;
        case 'skip_rest':
          _handleSkipRest(parameters);
          break;
        case 'next_exercise':
          _handleNextExercise(parameters);
          break;
        case 'end_workout':
          _handleEndWorkout(parameters);
          break;
        case 'counter_control':
          _handleCounterControl(parameters);
          break;
        case 'get_workout_status':
          _handleGetWorkoutStatus(parameters);
          break;
        default:
          debugPrint('⚠️ Unknown voice workout tool: $toolName');
      }
    };
  }
  
  /// Handle get_workout_status request
  void _handleGetWorkoutStatus(Map<String, dynamic> parameters) {
    debugPrint('📊 Voice command: Get workout status');
    
    if (_currentWorkout != null) {
      // Send current workout status as a message
      final statusMessage = _generateWorkoutContext(_currentWorkout!);
      
      // You would need to add a method to send messages back to the conversation
      // For now, we'll just log it
      debugPrint('📊 Current status: $statusMessage');
    }
  }

  /// Handle start_exercise voice command
  void _handleStartExercise(Map<String, dynamic> parameters) {
    final exerciseName = parameters['exerciseName'] as String?;
    debugPrint('🏋️ Voice command: Start exercise - $exerciseName');
    
    // Find exercise by name and set it as current
    if (_currentWorkout != null && exerciseName != null) {
      final exerciseIndex = _currentWorkout!.exercises
          .indexWhere((e) => e.name.toLowerCase().contains(exerciseName.toLowerCase()));
      
      if (exerciseIndex != -1) {
        // Update session data to trigger exercise navigation
        _workoutNotifier.updateSessionData({
          'voice_goto_exercise': exerciseIndex,
          'goto_exercise_timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        
        debugPrint('✅ Exercise found: ${_currentWorkout!.exercises[exerciseIndex].name}');
        debugPrint('✅ Navigating to exercise index: $exerciseIndex');
      } else {
        debugPrint('❌ Exercise not found: $exerciseName');
        // Try partial matching
        final partialMatch = _currentWorkout!.exercises
            .indexWhere((e) => exerciseName.toLowerCase().split(' ').any((word) => 
                e.name.toLowerCase().contains(word)));
        
        if (partialMatch != -1) {
          _workoutNotifier.updateSessionData({
            'voice_goto_exercise': partialMatch,
            'goto_exercise_timestamp': DateTime.now().millisecondsSinceEpoch,
          });
          debugPrint('✅ Partial match found: ${_currentWorkout!.exercises[partialMatch].name}');
        }
      }
    }
  }

  /// Handle count_reps voice command
  void _handleCountReps(Map<String, dynamic> parameters) {
    final count = parameters['count'];
    final reps = count is int ? count : (count as double?)?.toInt() ?? 0;
    
    debugPrint('📊 Voice command: Count reps - $reps');
    
    // Update the current reps in the session data
    // This will be used by the UI to update the rep counter
    if (_currentWorkout != null) {
      _workoutNotifier.updateSessionData({'current_reps': reps});
      debugPrint('✅ Updated current reps to: $reps');
    }
  }

  /// Handle complete_set voice command
  void _handleCompleteSet(Map<String, dynamic> parameters) {
    debugPrint('✅ Voice command: Complete set');
    
    if (_currentWorkout != null) {
      final currentExercise = _currentWorkout!.currentExercise;
      if (currentExercise != null) {
        // Get current reps from session data or use target reps
        final sessionState = _ref.read(workoutSessionProvider);
        final sessionData = sessionState.sessionData;
        final currentReps = sessionData['current_reps'] as int?;
        final currentWeight = sessionData['current_weight'] as double?;
        
        // Use target values if no current values set
        final targetReps = currentExercise.targetReps.isNotEmpty ? currentExercise.targetReps.first : 10;
        final targetWeight = currentExercise.targetWeights.isNotEmpty ? currentExercise.targetWeights.first : 0.0;
        
        final performedReps = currentReps ?? targetReps;
        final performedWeight = currentWeight ?? targetWeight;
        
        // Complete the set
        _workoutNotifier.completeSet(
          performedReps: performedReps,
          performedWeight: performedWeight,
        );
        
        debugPrint('✅ Set completed: $performedReps reps @ ${performedWeight}lbs');
      }
    }
  }

  /// Handle start_rest voice command
  void _handleStartRest(Map<String, dynamic> parameters) {
    final duration = parameters['duration'] as int? ?? 60;
    debugPrint('⏸️ Voice command: Start rest - ${duration}s');
    
    // Update session data to trigger rest period
    _workoutNotifier.updateSessionData({
      'voice_start_rest': true,
      'rest_duration': duration,
      'rest_timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    
    debugPrint('✅ Rest period initiated via voice');
  }

  /// Handle skip_rest voice command
  void _handleSkipRest(Map<String, dynamic> parameters) {
    debugPrint('⏭️ Voice command: Skip rest');
    
    // Update session data to trigger rest skip
    _workoutNotifier.updateSessionData({
      'voice_skip_rest': true,
      'skip_rest_timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    
    debugPrint('✅ Rest period skipped via voice');
  }

  /// Handle next_exercise voice command
  void _handleNextExercise(Map<String, dynamic> parameters) {
    debugPrint('➡️ Voice command: Next exercise');
    
    // Move to next exercise using the workout session provider
    _workoutNotifier.nextExercise();
    debugPrint('✅ Moved to next exercise');
  }

  /// Handle end_workout voice command
  void _handleEndWorkout(Map<String, dynamic> parameters) {
    debugPrint('🏁 Voice command: End workout');
    
    // Complete the workout
    _workoutNotifier.completeWorkout(
      userRating: null,
      userFeedback: 'Workout completed via voice command',
    );
    
    debugPrint('✅ Workout completed via voice');
  }

  /// Handle counter_control voice command
  void _handleCounterControl(Map<String, dynamic> parameters) {
    final action = parameters['action'] as String?;
    final value = parameters['value'] as int?;
    
    debugPrint('🔢 Voice command: Counter control - action: $action, value: $value');
    
    // Get current reps from session data
    final sessionState = _ref.read(workoutSessionProvider);
    final currentReps = sessionState.sessionData['current_reps'] as int? ?? 0;
    
    int newReps = currentReps;
    
    switch (action) {
      case 'increment':
        newReps = currentReps + (value ?? 1);
        break;
      case 'decrement':
        newReps = (currentReps - (value ?? 1)).clamp(0, 999);
        break;
      case 'set':
        newReps = value ?? currentReps;
        break;
      case 'reset':
        newReps = 0;
        break;
      default:
        debugPrint('⚠️ Unknown counter action: $action');
        return;
    }
    
    // Update the reps counter
    _workoutNotifier.updateSessionData({'current_reps': newReps});
    debugPrint('✅ Counter updated to: $newReps');
  }
}

/// Provider for voice workout service
final voiceWorkoutServiceProvider = ChangeNotifierProvider<VoiceWorkoutService>((ref) {
  final voiceService = SimpleElevenLabsService();
  final workoutNotifier = ref.read(workoutSessionProvider.notifier);
  
  final service = VoiceWorkoutService(voiceService, workoutNotifier, ref);
  
  // Watch for workout session changes to update current workout
  ref.listen(workoutSessionProvider, (previous, next) {
    if (next.currentWorkout != null && next.currentWorkout != previous?.currentWorkout) {
      service._currentWorkout = next.currentWorkout;
      debugPrint('🔄 Voice service updated with new workout: ${next.currentWorkout?.name}');
    }
  });
  
  return service;
}); 