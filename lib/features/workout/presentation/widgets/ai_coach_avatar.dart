import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/services/voice_workout_service.dart';
import '../../domain/providers/workout_session_provider.dart';

class AICoachAvatar extends ConsumerStatefulWidget {
  final String? message;
  final VoidCallback? onTap;
  final double size;
  final bool enableVoice;
  final dynamic currentWorkout; // Accept current workout data

  const AICoachAvatar({
    super.key,
    this.message,
    this.onTap,
    this.size = 60,
    this.enableVoice = false,
    this.currentWorkout,
  });

  @override
  ConsumerState<AICoachAvatar> createState() => _AICoachAvatarState();
}

class _AICoachAvatarState extends ConsumerState<AICoachAvatar>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  
  bool _showMessage = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startPulseAnimation();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(
      CurvedAnimation(
        parent: _bounceController,
        curve: Curves.elasticOut,
      ),
    );
  }

  void _startPulseAnimation() {
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceService = ref.watch(voiceWorkoutServiceProvider);
    final isVoiceActive = voiceService.isVoiceActive;
    final voiceStatus = voiceService.voiceStatus;
    
    return GestureDetector(
      onTap: _handleTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Message bubble
          if (_showMessage && widget.message != null)
            _buildMessageBubble(),
          
          // Avatar
          AnimatedBuilder(
            animation: Listenable.merge([_pulseController, _bounceController]),
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value * _bounceAnimation.value,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Voice status indicator ring
                    if (isVoiceActive)
                      Container(
                        width: widget.size + 20,
                        height: widget.size + 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _getVoiceStatusColor(voiceStatus),
                            width: 3,
                          ),
                        ),
                      ),
                    
                    // Main avatar container
                    Container(
                      width: widget.size,
                      height: widget.size,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColorPalette.primaryOrange,
                            AppColorPalette.primaryOrangeLight,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(widget.size / 2),
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // AI icon - changes based on voice status
                          Center(
                            child: Icon(
                              isVoiceActive ? Icons.mic : Icons.psychology,
                              color: Colors.white,
                              size: widget.size * 0.5,
                            ),
                          ),
                          
                          // Voice status indicator
                          if (isVoiceActive)
                            Positioned(
                              bottom: 4,
                              right: 4,
                              child: Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: _getVoiceStatusColor(voiceStatus),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                          
                          // Notification dot
                          if (widget.message != null && !isVoiceActive)
                            Positioned(
                              top: 4,
                              right: 4,
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: AppColorPalette.successGreen,
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
  
  Color _getVoiceStatusColor(dynamic status) {
    if (status == null) return Colors.grey;
    
    switch (status.toString()) {
      case 'connected':
        return AppColorPalette.successGreen;
      case 'speaking':
        return Colors.blue;
      case 'listening':
        return AppColorPalette.primaryOrange;
      case 'connecting':
        return Colors.yellow;
      case 'error':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildMessageBubble() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      constraints: const BoxConstraints(maxWidth: 200),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.message!,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildResponseButton('👍', () => _handleResponse(true)),
              const SizedBox(width: 8),
              _buildResponseButton('👎', () => _handleResponse(false)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResponseButton(String emoji, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  void _handleTap() async {
    HapticFeedback.lightImpact();
    
    _bounceController.forward().then((_) {
      _bounceController.reverse();
    });

    if (widget.message != null) {
      setState(() {
        _showMessage = !_showMessage;
      });
    }

    // Voice activation for workout mode
    if (widget.enableVoice) {
      final voiceService = ref.read(voiceWorkoutServiceProvider);
      
      if (!voiceService.isVoiceActive) {
        // Start voice workout mode
        _showVoiceActivationDialog();
      } else {
        // Stop voice workout mode
        await voiceService.stopVoiceWorkout();
      }
    }

    widget.onTap?.call();
  }

  void _showVoiceActivationDialog() {
    debugPrint('🎤 Showing voice activation dialog');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColorPalette.darkCard,
        title: Row(
          children: [
            Icon(Icons.mic, color: AppColorPalette.primaryOrange),
            const SizedBox(width: 8),
            const Text(
              'Voice Workout',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        content: const Text(
          'Activate hands-free mode with Nathan? He\'ll guide you through your workout using voice commands.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              debugPrint('🎤 Activate button pressed');
              Navigator.pop(context);
              _activateVoiceWorkout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorPalette.primaryOrange,
            ),
            child: const Text(
              'Activate',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _activateVoiceWorkout() async {
    final voiceService = ref.read(voiceWorkoutServiceProvider);
    final workoutSession = ref.read(workoutSessionProvider);
    
    try {
      debugPrint('🎤 Attempting to activate voice workout...');
      
      // Get workout from session provider first, fallback to widget prop
      final currentWorkout = workoutSession.currentWorkout ?? widget.currentWorkout;
      
      // Start voice workout with proper context
      if (currentWorkout != null) {
        await voiceService.startVoiceWorkout(currentWorkout);
        debugPrint('✅ Voice workout started with context for: ${currentWorkout.name}');
      } else {
        debugPrint('⚠️ No workout context available, starting basic conversation');
        // Fallback to basic conversation if no workout context
        await voiceService.voiceService.startConversation();
        debugPrint('✅ Basic voice conversation started');
      }
      
      // Show activation feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.mic, color: AppColorPalette.primaryOrange),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentWorkout != null 
                    ? 'Voice activated! Nathan is ready to guide your ${currentWorkout.name} workout.'
                    : 'Voice activated! Nathan is ready to assist you.',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: AppColorPalette.darkCard,
          duration: const Duration(seconds: 5),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error activating voice workout: $e');
      
      // Show error feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to activate voice mode: $e',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red.withOpacity(0.8),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _handleResponse(bool isPositive) {
    HapticFeedback.lightImpact();
    
    setState(() {
      _showMessage = false;
    });

    // TODO: Send feedback to AI coach system
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isPositive ? 'Thanks for the feedback!' : 'We\'ll adjust accordingly.',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void showMessage(String message) {
    if (mounted) {
      setState(() {
        _showMessage = true;
      });
      
      // Auto-hide after 10 seconds
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          setState(() {
            _showMessage = false;
          });
        }
      });
    }
  }

  void hideMessage() {
    if (mounted) {
      setState(() {
        _showMessage = false;
      });
    }
  }
}

/// AI Coach messages for different workout scenarios
class AICoachMessages {
  static const List<String> encouragement = [
    "You're doing great! Keep it up! 💪",
    "Perfect form! You've got this! 🔥",
    "Strong work! Push through! ⚡",
    "Excellent! Feel that burn! 🌟",
    "Amazing effort! Stay focused! 🎯",
  ];

  static const List<String> formReminders = [
    "Remember to breathe steadily 🫁",
    "Keep your core engaged 💪",
    "Control the movement 🎯",
    "Full range of motion 📏",
    "Quality over quantity! ✨",
  ];

  static const List<String> restReminders = [
    "Take your time to recover 😌",
    "Hydrate and breathe deeply 💧",
    "Prepare for the next set 🔥",
    "You're halfway there! 🎯",
    "Rest well, finish strong! 💪",
  ];

  static const List<String> completion = [
    "Incredible work today! 🏆",
    "You crushed it! Well done! 🎉",
    "Another step closer to your goals! 🌟",
    "That's how it's done! 💪",
    "You should be proud! Amazing! ✨",
  ];

  static String getRandomMessage(List<String> messages) {
    return messages[DateTime.now().millisecond % messages.length];
  }

  static String getEncouragement() => getRandomMessage(encouragement);
  static String getFormReminder() => getRandomMessage(formReminders);
  static String getRestReminder() => getRandomMessage(restReminders);
  static String getCompletion() => getRandomMessage(completion);
}
