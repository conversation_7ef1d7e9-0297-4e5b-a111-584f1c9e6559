import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/services/voice_workout_service.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../../../../core/theme/color_palette.dart';

class HandsFreeWorkoutScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;
  const HandsFreeWorkoutScreen({super.key, required this.workout});

  @override
  ConsumerState<HandsFreeWorkoutScreen> createState() => _HandsFreeWorkoutScreenState();
}

class _HandsFreeWorkoutScreenState extends ConsumerState<HandsFreeWorkoutScreen> {
  @override
  void initState() {
    super.initState();
    // Start the voice workout with proper dynamic variables
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startVoiceWorkout();
    });
  }
  
  Future<void> _startVoiceWorkout() async {
    final voiceService = ref.read(voiceWorkoutServiceProvider);
    try {
      await voiceService.startVoiceWorkout(widget.workout);
    } catch (e) {
      debugPrint('Failed to start voice workout: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start voice mode: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    // Stop the voice workout when leaving the screen
    final voiceService = ref.read(voiceWorkoutServiceProvider);
    voiceService.stopVoiceWorkout();
    super.dispose();
  }
  
  Color _getStatusColor(dynamic status) {
    final statusString = status?.toString() ?? '';
    switch (statusString) {
      case 'connected':
      case 'speaking':
        return Colors.green;
      case 'listening':
        return AppColorPalette.primaryOrange;
      case 'connecting':
        return Colors.yellow;
      case 'error':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  IconData _getStatusIcon(dynamic status) {
    final statusString = status?.toString() ?? '';
    switch (statusString) {
      case 'connected':
        return Icons.check_circle;
      case 'speaking':
        return Icons.record_voice_over;
      case 'listening':
        return Icons.mic;
      case 'connecting':
        return Icons.sync;
      case 'error':
        return Icons.error;
      default:
        return Icons.circle;
    }
  }
  
  String _getStatusText(dynamic status) {
    final statusString = status?.toString() ?? '';
    switch (statusString) {
      case 'connected':
        return 'Connected';
      case 'speaking':
        return 'Speaking...';
      case 'listening':
        return 'Listening...';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Connection Error';
      default:
        return 'Offline';
    }
  }

  @override
  Widget build(BuildContext context) {
    final voiceService = ref.watch(voiceWorkoutServiceProvider);
    final sessionState = ref.watch(workoutSessionProvider);
    final workout = sessionState.currentWorkout ?? widget.workout;
    final currentExercise = workout.currentExercise;
    final currentSetNum = workout.currentSetIndex + 1;
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Hands-Free Workout'),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      workout.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    if (currentExercise != null) ...[
                      Text(
                        currentExercise.name,
                        style: TextStyle(
                          color: AppColorPalette.primaryOrange,
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Set $currentSetNum of ${currentExercise.sets}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 18,
                        ),
                      ),
                    ],
                    const SizedBox(height: 32),
                    // Voice status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: _getStatusColor(voiceService.voiceStatus).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: _getStatusColor(voiceService.voiceStatus),
                          width: 2,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getStatusIcon(voiceService.voiceStatus),
                            color: _getStatusColor(voiceService.voiceStatus),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _getStatusText(voiceService.voiceStatus),
                            style: TextStyle(
                              color: _getStatusColor(voiceService.voiceStatus),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    if (voiceService.voiceService.lastTranscript.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Text(
                          'You: ${voiceService.voiceService.lastTranscript}',
                          style: const TextStyle(color: Colors.white70),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    if (voiceService.voiceService.lastResponse.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Text(
                          'Coach: ${voiceService.voiceService.lastResponse}',
                          style: const TextStyle(color: AppColorPalette.primaryOrange),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    await voiceService.stopVoiceWorkout();
                    if (mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColorPalette.primaryOrange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    'Finish Workout',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
