import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../../domain/services/voice_workout_service.dart';
import '../widgets/ai_coach_avatar.dart';

class HandsFreeModeScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;

  const HandsFreeModeScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<HandsFreeModeScreen> createState() => _HandsFreeModeScreenState();
}

class _HandsFreeModeScreenState extends ConsumerState<HandsFreeModeScreen> {
  @override
  void initState() {
    super.initState();
    // Auto-start voice mode after a short delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 1), () {
        _startVoiceMode();
      });
    });
  }

  void _startVoiceMode() async {
    final voiceService = ref.read(voiceWorkoutServiceProvider);
    try {
      // Send initial context as a message instead of relying on dynamic variables
      await voiceService.startVoiceWorkout(widget.workout);
      
      // Send explicit context message
      Future.delayed(const Duration(seconds: 2), () {
        _sendWorkoutContext();
      });
    } catch (e) {
      debugPrint('Failed to start voice mode: $e');
    }
  }

  void _sendWorkoutContext() {
    final workout = widget.workout;
    final currentExercise = workout.currentExercise;
    
    if (currentExercise != null) {
      final contextMessage = '''
I'm starting my ${workout.name} workout. 
I'm on ${currentExercise.name}, set ${workout.currentSetIndex + 1} of ${currentExercise.sets}.
Target is ${currentExercise.targetReps[workout.currentSetIndex]} reps.
      ''';
      
      // This would need to be implemented in the voice service
      // voiceService.sendTextMessage(contextMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    final sessionState = ref.watch(workoutSessionProvider);
    final voiceService = ref.watch(voiceWorkoutServiceProvider);
    final workout = sessionState.currentWorkout ?? widget.workout;
    final currentExercise = workout.currentExercise;
    
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: Stack(
          children: [
            // Simple, large UI elements
            Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () => _showExitDialog(),
                        icon: const Icon(Icons.close, color: Colors.white, size: 32),
                      ),
                      Text(
                        'Hands-Free Mode',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 48), // Balance the layout
                    ],
                  ),
                ),
                
                // Main content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Large workout name
                        Text(
                          workout.name,
                          style: AppTypography.h1.copyWith(
                            color: AppColorPalette.primaryOrange,
                            fontSize: 36,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 40),
                        
                        // Current exercise card
                        if (currentExercise != null) ...[
                          GlassMorphismCard(
                            child: Padding(
                              padding: const EdgeInsets.all(32),
                              child: Column(
                                children: [
                                  Text(
                                    currentExercise.name,
                                    style: AppTypography.h2.copyWith(
                                      color: Colors.white,
                                      fontSize: 28,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 24),
                                  
                                  // Large set indicator
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 40,
                                      vertical: 20,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColorPalette.primaryOrange.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: AppColorPalette.primaryOrange,
                                        width: 2,
                                      ),
                                    ),
                                    child: Text(
                                      'SET ${workout.currentSetIndex + 1} of ${currentExercise.sets}',
                                      style: AppTypography.h3.copyWith(
                                        color: AppColorPalette.primaryOrange,
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  
                                  // Target reps
                                  Text(
                                    'Target: ${currentExercise.targetReps[workout.currentSetIndex]} reps',
                                    style: AppTypography.h3.copyWith(
                                      color: Colors.white70,
                                      fontSize: 20,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                        
                        const SizedBox(height: 40),
                        
                        // Voice status
                        if (voiceService.isVoiceActive) ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.mic,
                                color: _getVoiceStatusColor(voiceService.voiceStatus),
                                size: 32,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                _getVoiceStatusText(voiceService.voiceStatus),
                                style: AppTypography.body1.copyWith(
                                  color: _getVoiceStatusColor(voiceService.voiceStatus),
                                  fontSize: 18,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                // Bottom instruction
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'Say commands like "Done", "12 reps", "Next exercise", or "Rest"',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white60,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            
            // Floating AI Coach Avatar
            Positioned(
              bottom: 100,
              right: 20,
              child: AICoachAvatar(
                size: 80,
                enableVoice: true,
                currentWorkout: workout,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getVoiceStatusColor(dynamic status) {
    switch (status?.toString()) {
      case 'connected':
        return AppColorPalette.successGreen;
      case 'speaking':
        return Colors.blue;
      case 'listening':
        return AppColorPalette.primaryOrange;
      case 'connecting':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  String _getVoiceStatusText(dynamic status) {
    switch (status?.toString()) {
      case 'connected':
        return 'Ready';
      case 'speaking':
        return 'Nathan is speaking...';
      case 'listening':
        return 'Listening...';
      case 'connecting':
        return 'Connecting...';
      default:
        return 'Voice inactive';
    }
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColorPalette.darkCard,
        title: const Text(
          'Exit Hands-Free Mode?',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Your workout progress will be saved.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.go('/active-workout');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorPalette.primaryOrange,
            ),
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }
}