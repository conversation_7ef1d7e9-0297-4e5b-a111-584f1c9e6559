import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/presentation/screens/dashboard_screen.dart';
import '../../../workout/presentation/screens/current_workout_screen.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../../../chat/presentation/screens/chat_screen.dart';
import '../../../../shared/widgets/floating_chat_widget.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _navigationController;
  late Animation<double> _navigationAnimation;

  final List<Widget> _screens = [
    const DashboardScreen(),  // Using dashboard screen instead
    const CurrentWorkoutScreen(),
    const StatisticsScreen(),
    const ChatScreen(),
    const ProfileScreen(),
  ];

  final List<GlassNavigationItem> _navigationItems = [
    const GlassNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
    ),
    const GlassNavigationItem(
      icon: Icons.fitness_center_outlined,
      activeIcon: Icons.fitness_center,
      label: 'Workout',
    ),
    const GlassNavigationItem(
      icon: Icons.analytics_outlined,
      activeIcon: Icons.analytics,
      label: 'Stats',
    ),
    const GlassNavigationItem(
      icon: Icons.mic_outlined,
      activeIcon: Icons.mic,
      label: 'Voice',
    ),
    const GlassNavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _navigationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _navigationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _navigationController,
        curve: Curves.easeInOut,
      ),
    );

    _navigationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _navigationController.dispose();
    super.dispose();
  }

  void _onNavigationTap(int index) {
    if (index != _currentIndex) {
      HapticFeedback.lightImpact();
      setState(() {
        _currentIndex = index;
      });
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOutCubic,
      );
    }
  }

  void _onPageChanged(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: Stack(
        children: [
          PageView(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            physics: const BouncingScrollPhysics(),
            children: _screens,
          ),
          const FloatingChatWidget(),
        ],
      ),
      bottomNavigationBar: AnimatedBuilder(
        animation: _navigationAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, 100 * (1 - _navigationAnimation.value)),
            child: Opacity(
              opacity: _navigationAnimation.value,
              child: GlassNavigationBar(
                items: _navigationItems,
                currentIndex: _currentIndex,
                onTap: _onNavigationTap,
                margin: const EdgeInsets.only(
                  left: 16,
                  right: 16,
                  bottom: 32,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Placeholder screen for statistics
class StatisticsScreen extends StatelessWidget {
  const StatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Statistics'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Statistics Screen\n(To be implemented)',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
}
