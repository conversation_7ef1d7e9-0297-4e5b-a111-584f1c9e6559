import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/simple_conversation_models.dart';

class SimpleElevenLabsService extends ChangeNotifier {
  static final SimpleElevenLabsService _instance = SimpleElevenLabsService._internal();
  factory SimpleElevenLabsService() => _instance;
  SimpleElevenLabsService._internal();

  // Connection
  WebSocketChannel? _channel;
  final String _agentId = dotenv.env['ELEVENLABS_AGENT_ID'] ?? '';
  
  // Audio
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _audioListenerSetup = false;
  
  // Status
  SimpleConversationStatus _status = SimpleConversationStatus.disconnected;
  SimpleConversationStatus get status => _status;
  
  String _lastTranscript = '';
  String get lastTranscript => _lastTranscript;
  
  String _lastResponse = '';
  String get lastResponse => _lastResponse;
  
  // Update status
  void _updateStatus(SimpleConversationStatus newStatus) {
    _status = newStatus;
    notifyListeners();
  }
  
  // Callback for client tools
  Function(String toolName, Map<String, dynamic> parameters)? onClientToolCall;
  
  // Initial context to send after connection
  String? _initialContext;

  // Start conversation with optional initial context and dynamic variables
  Future<void> startConversation({String? initialContext, Map<String, dynamic>? customVariables}) async {
    _initialContext = initialContext;
    try {
      // Check microphone permission
      final status = await Permission.microphone.request();
      if (!status.isGranted) {
        _updateStatus(SimpleConversationStatus.error);
        return;
      }

      // Connect to WebSocket
      _updateStatus(SimpleConversationStatus.connecting);

      // Get API key from environment
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];
      if (apiKey == null || apiKey.isEmpty || apiKey == 'your_api_key_here') {
        debugPrint('❌ ElevenLabs API key not configured properly');
        _updateStatus(SimpleConversationStatus.error);
        return;
      }
      
      // Build URL with dynamic variables if provided
      var wsUrl = 'wss://api.elevenlabs.io/v1/convai/conversation?agent_id=$_agentId&xi-api-key=$apiKey';
      
      // Add custom variables as individual query parameters as per ElevenLabs docs
      if (customVariables != null && customVariables.isNotEmpty) {
        customVariables.forEach((key, value) {
          wsUrl += '&$key=${Uri.encodeComponent(value.toString())}';
        });
        debugPrint('🔧 Added custom variables: $customVariables');
      }
      
      debugPrint('🔗 Connecting to: ${wsUrl.replaceAll(apiKey, "***")}');
      
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to messages
      _channel!.stream.listen(
        _handleMessage,
        onError: (error) {
          debugPrint('❌ WebSocket error: $error');
          _updateStatus(SimpleConversationStatus.error);
        },
        onDone: () {
          final closeCode = _channel?.closeCode;
          final closeReason = _channel?.closeReason;
          
          debugPrint('🔌 WebSocket closed');
          debugPrint('📊 Close code: ${closeCode ?? "unknown"}');
          debugPrint('📊 Close reason: ${closeReason ?? "no reason given"}');
          
          // Common WebSocket close codes:
          // 1000: Normal closure
          // 1006: Abnormal closure (no close frame)
          // 1008: Policy violation
          // 1011: Unexpected condition
          if (closeCode != null && closeCode != 1000) {
            debugPrint('❌ Abnormal WebSocket closure detected!');
            if (closeCode == 1006) {
              debugPrint('❌ Connection lost unexpectedly - possible network or authentication issue');
            } else if (closeCode == 1008) {
              debugPrint('❌ Policy violation - check authentication or message format');
            } else if (closeCode == 1011) {
              debugPrint('❌ Server encountered unexpected condition');
            }
          }
          
          stopConversation();
        },
      );
      
      // Update status first
      _updateStatus(SimpleConversationStatus.connected);
      
      // Start recording after status update
      await _startRecording();
      
      // Send initial context if provided, with a longer delay
      if (_initialContext != null) {
        // Wait for agent to be fully ready
        await Future.delayed(const Duration(milliseconds: 1500));
        await _sendInitialContext(_initialContext!);
      }
      
    } catch (e) {
      debugPrint('❌ Error starting conversation: $e');
      _updateStatus(SimpleConversationStatus.error);
    }
  }
  
  // Send initial context message after connection
  Future<void> _sendInitialContext(String context) async {
    try {
      // Wait for connection to be established
      await Future.delayed(const Duration(milliseconds: 800));
      
      // Send a user message with the workout context
      final message = {
        'type': 'user_message',
        'user_message': {
          'message': context
        }
      };
      
      if (_channel != null) {
        _channel!.sink.add(jsonEncode(message));
        debugPrint('📨 Initial context sent: $context');
      }
    } catch (e) {
      debugPrint('❌ Error sending initial context: $e');
    }
  }
  
  // Stop conversation
  Future<void> stopConversation() async {
    await _stopRecording();
    await _audioPlayer.stop();
    await _channel?.sink.close();
    _channel = null;
    _resumeRecordingTimer?.cancel();
    _resumeRecordingTimer = null;
    _activeAudioCount = 0;
    _audioListenerSetup = false; // Reset for next conversation
    _updateStatus(SimpleConversationStatus.disconnected);
  }
  
  // Handle incoming messages
  void _handleMessage(dynamic data) {
    try {
      final json = jsonDecode(data);
      debugPrint('Received message type: ${json['type']}');
      
      // Handle ping messages first to prevent connection closure
      if (json['type'] == 'ping') {
        debugPrint('📥 RAW PING MESSAGE: $json');
        _handleRawPing(json);
        return;
      }
      
      final message = SimpleConversationMessage.fromJson(json);
      
      if (message is UserTranscript) {
        debugPrint('User transcript: ${message.text}');
        _lastTranscript = message.text;
        _updateStatus(SimpleConversationStatus.speaking);
        notifyListeners();
      } else if (message is AgentResponse) {
        debugPrint('Agent response: ${message.text}');
        _lastResponse = message.text;
        // Immediately pause recording when agent starts responding to prevent echo
        _pauseRecordingForText();
        debugPrint('🎤 Recording paused for incoming agent response');
        notifyListeners();
      } else if (message is AudioMessage) {
        debugPrint('Received audio message');
        // Enhanced echo prevention - pause recording for ALL audio
        _pauseRecordingForAudio();
        // Play audio response
        _playAudio(message.audioBase64);
      } else if (message is PingMessage) {
        // This shouldn't be reached due to the check above, but keep as fallback
        debugPrint('📥 Ping received via parser - sending pong');
        final eventId = message.eventId ?? DateTime.now().millisecondsSinceEpoch;
        _sendMessage(PongMessage(eventId: eventId));
      }

      // Handle client tool calls
      if (json['type'] == 'client_tool_call') {
        _handleClientToolCall(json);
      }
      
      // Handle unknown message types that don't break functionality
      if (json['type'] == 'interruption' || 
          json['type'] == 'agent_response_correction') {
        debugPrint('ℹ️ Received ${json['type']} message (handled gracefully)');
        // Pause recording for these messages too as they may precede audio
        _pauseRecordingForText();
        debugPrint('🎤 Recording paused for ${json['type']} message');
      }
    } catch (e) {
      debugPrint('Error handling message: $e');
      debugPrint('Raw data: $data');
      
      // Try to handle ping messages even if parsing fails
      try {
        final json = jsonDecode(data);
        if (json['type'] == 'ping') {
          debugPrint('📥 Fallback ping handling after error');
          _handleRawPing(json);
        }
      } catch (e2) {
        debugPrint('Failed to handle ping in error recovery: $e2');
      }
    }
  }

  /// Handle client tool calls
  void _handleClientToolCall(Map<String, dynamic> json) {
    try {
      debugPrint('🔧 Client tool call received: $json');

      final toolCall = json['client_tool_call'] as Map<String, dynamic>?;
      if (toolCall == null) return;

      final toolName = toolCall['tool_name'] as String?;
      final parameters = toolCall['parameters'] as Map<String, dynamic>? ?? {};
      final callId = toolCall['call_id'] as String?;

      if (toolName != null && onClientToolCall != null) {
        // Execute the tool callback
        onClientToolCall!(toolName, parameters);

        // Send success response back to ElevenLabs
        if (callId != null) {
          final response = {
            'type': 'client_tool_result',
            'client_tool_result': {
              'call_id': callId,
              'result': 'success',
              'output': 'Tool executed successfully'
            }
          };

          if (_channel != null) {
            _channel!.sink.add(jsonEncode(response));
            debugPrint('✅ Tool result sent back to agent');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error handling client tool call: $e');
    }
  }

  /// Handle raw ping message
  void _handleRawPing(Map<String, dynamic> json) {
    try {
      // Extract event ID if available
      int eventId = DateTime.now().millisecondsSinceEpoch;
      
      if (json.containsKey('ping_event')) {
        final pingEvent = json['ping_event'] as Map<String, dynamic>?;
        eventId = pingEvent?['event_id'] as int? ?? eventId;
      } else if (json.containsKey('event_id')) {
        eventId = json['event_id'] as int? ?? eventId;
      }
      
      // Use the proper PongMessage class with the correct event ID
      final pongMessage = PongMessage(eventId: eventId);
      
      if (_channel != null) {
        try {
          _channel!.sink.add(jsonEncode(pongMessage.toJson()));
          debugPrint('🏓 Pong sent for ping event $eventId');
          debugPrint('📤 PONG RESPONSE: ${pongMessage.toJson()}');
        } catch (e) {
          debugPrint('❌ Error sending pong: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Error handling raw ping: $e');
    }
  }
  
  // Send message
  void _sendMessage(SimpleConversationMessage message) {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode(message.toJson()));
    }
  }
  
  // Recording stream subscription
  StreamSubscription<Uint8List>? _recordingSubscription;
  
  // Enhanced echo prevention tracking
  int _activeAudioCount = 0;
  Timer? _resumeRecordingTimer;
  bool _audioSessionActive = false;
  
  // Start recording
  Future<void> _startRecording() async {
    try {
      // Setup audio player listener once
      if (!_audioListenerSetup) {
        _audioPlayer.onPlayerComplete.listen((_) {
          debugPrint('🔊 Audio playback completed');
          _scheduleRecordingResume();
        });
        _audioListenerSetup = true;
        debugPrint('🎧 Audio completion listener setup');
      }
      
      if (await _recorder.hasPermission()) {
        // Start streaming audio with enhanced settings for echo cancellation
        final stream = await _recorder.startStream(
          const RecordConfig(
            encoder: AudioEncoder.pcm16bits,
            sampleRate: 16000,
            numChannels: 1,
            autoGain: true,
            echoCancel: true,
            noiseSuppress: true,
          ),
        );
        
        // Keep reference to subscription
        _recordingSubscription = stream.listen(
          (chunk) {
            if (_channel != null) {
              final base64Audio = base64Encode(chunk);
              // Send audio chunk in the official ElevenLabs format
              final audioChunkMessage = {
                'user_audio_chunk': base64Audio,
              };
              _channel!.sink.add(jsonEncode(audioChunkMessage));
            }
          },
          onError: (error) {
            debugPrint('Recording stream error: $error');
          },
          onDone: () {
            debugPrint('Recording stream ended');
          },
        );
        
        debugPrint('Recording started successfully with echo cancellation');
      }
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
  }
  
  // Stop recording
  Future<void> _stopRecording() async {
    try {
      await _recordingSubscription?.cancel();
      _recordingSubscription = null;
      await _recorder.stop();
      _resumeRecordingTimer?.cancel();
      _resumeRecordingTimer = null;
      _activeAudioCount = 0;
      _audioSessionActive = false;
      debugPrint('Recording stopped');
    } catch (e) {
      debugPrint('Error stopping recording: $e');
    }
  }
  
  // Enhanced echo prevention methods
  void _pauseRecordingForAudio() {
    if (_recordingSubscription?.isPaused == false) {
      _recordingSubscription?.pause();
      debugPrint('🎤 Recording paused for audio playback (echo prevention)');
    }
    
    // Only increment counter for the first audio message in a session
    if (!_audioSessionActive) {
      _activeAudioCount++;
      _audioSessionActive = true;
      debugPrint('🎵 New audio session started. Active count: $_activeAudioCount');
    } else {
      debugPrint('🎵 Additional audio in existing session. Count unchanged: $_activeAudioCount');
    }
    
    // Cancel any pending resume timer
    _resumeRecordingTimer?.cancel();
  }
  
  void _pauseRecordingForText() {
    if (_recordingSubscription?.isPaused == false) {
      _recordingSubscription?.pause();
      debugPrint('🎤 Recording paused for text response (echo prevention)');
    }
    // Don't increment counter for text-only responses
    // Cancel any pending resume timer and schedule a quick resume
    _resumeRecordingTimer?.cancel();
    _resumeRecordingTimer = Timer(const Duration(milliseconds: 500), () {
      if (_recordingSubscription?.isPaused == true) {
        _recordingSubscription?.resume();
        debugPrint('🎤 Recording resumed after text response delay');
      }
    });
  }
  
  void _scheduleRecordingResume() {
    if (_audioSessionActive) {
      _activeAudioCount--;
      _audioSessionActive = false;
      debugPrint('📊 Audio session completed. Active count: $_activeAudioCount');
    }
    
    if (_activeAudioCount <= 0) {
      _activeAudioCount = 0;
      // Schedule resume with optimized delay to prevent echo
      _resumeRecordingTimer?.cancel();
      debugPrint('⏰ Scheduling recording resume in 800ms...');
      _resumeRecordingTimer = Timer(const Duration(milliseconds: 800), () {
        // Only resume if recording is actually paused
        if (_recordingSubscription?.isPaused == true) {
          _recordingSubscription?.resume();
          debugPrint('🎤 Recording resumed after echo prevention delay');
        } else if (_recordingSubscription != null) {
          debugPrint('ℹ️ Recording was already active - no need to resume');
        } else {
          debugPrint('ℹ️ No recording subscription available to resume');
        }
      });
    } else {
      debugPrint('🔇 Still $_activeAudioCount audio sessions active, keeping recording paused');
    }
  }
  
  // Play audio
  Future<void> _playAudio(String base64Audio) async {
    try {
      // Stop any currently playing audio to prevent overlap
      await _audioPlayer.stop();
      
      _updateStatus(SimpleConversationStatus.listening);
      
      debugPrint('Audio base64 length: ${base64Audio.length}');
      
      if (base64Audio.isEmpty) {
        debugPrint('Warning: Empty audio data received');
        return;
      }
      
      final audioBytes = base64Decode(base64Audio);
      debugPrint('Decoded audio bytes length: ${audioBytes.length}');
      
      // Add WAV header to raw PCM data
      final wavBytes = _addWavHeader(audioBytes);
      debugPrint('WAV bytes length: ${wavBytes.length}');
      
      // Write to temporary file for iOS compatibility
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/elevenlabs_audio_${DateTime.now().millisecondsSinceEpoch}.wav');
      await tempFile.writeAsBytes(wavBytes);
      debugPrint('Audio file written to: ${tempFile.path}');
      
      // Configure audio session for better separation of input/output
      await _audioPlayer.setAudioContext(AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playAndRecord,
          options: const {
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.allowBluetooth,
            AVAudioSessionOptions.mixWithOthers,
          },
        ),
        android: const AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.speech,
          usageType: AndroidUsageType.voiceCommunication,
          audioFocus: AndroidAudioFocus.gain,
        ),
      ));
      
      // Play from file
      await _audioPlayer.play(DeviceFileSource(tempFile.path));
      debugPrint('Audio playback started');
      
      // Schedule cleanup (file cleanup is now handled by completion listener)
      Future.delayed(const Duration(seconds: 10), () {
        if (tempFile.existsSync()) {
          tempFile.deleteSync();
          debugPrint('Temp audio file cleaned up (delayed)');
        }
      });
    } catch (e) {
      debugPrint('Error playing audio: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }
  
  // Add WAV header to PCM data
  Uint8List _addWavHeader(Uint8List pcmData) {
    const int sampleRate = 16000;
    const int numChannels = 1;
    const int bitsPerSample = 16;
    
    final int dataSize = pcmData.length;
    final int fileSize = dataSize + 36;
    
    final wavHeader = Uint8List(44);
    final byteData = ByteData.view(wavHeader.buffer);
    
    // RIFF header
    wavHeader.setRange(0, 4, 'RIFF'.codeUnits);
    byteData.setUint32(4, fileSize, Endian.little);
    wavHeader.setRange(8, 12, 'WAVE'.codeUnits);
    
    // fmt chunk
    wavHeader.setRange(12, 16, 'fmt '.codeUnits);
    byteData.setUint32(16, 16, Endian.little); // fmt chunk size
    byteData.setUint16(20, 1, Endian.little); // PCM format
    byteData.setUint16(22, numChannels, Endian.little);
    byteData.setUint32(24, sampleRate, Endian.little);
    byteData.setUint32(28, sampleRate * numChannels * bitsPerSample ~/ 8, Endian.little);
    byteData.setUint16(32, numChannels * bitsPerSample ~/ 8, Endian.little);
    byteData.setUint16(34, bitsPerSample, Endian.little);
    
    // data chunk
    wavHeader.setRange(36, 40, 'data'.codeUnits);
    byteData.setUint32(40, dataSize, Endian.little);
    
    // Combine header and PCM data
    return Uint8List.fromList([...wavHeader, ...pcmData]);
  }
  
  @override
  void dispose() {
    stopConversation();
    _recorder.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }
}