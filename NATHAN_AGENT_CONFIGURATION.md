# Nathan Agent Configuration Guide

## The Issue
When activating voice control during a workout, <PERSON> responds with his generic greeting asking "What workout are we tackling today?" instead of acknowledging the current workout context.

## Root Cause
The issue is not in your Flutter code - it's in the ElevenLabs agent configuration. <PERSON>'s system prompt needs to be updated to handle workout context messages properly.

## Solution

### 1. Update Nathan's System Prompt in ElevenLabs Dashboard

Go to your Nathan agent settings and update the system prompt to include:

```
When a user sends a message starting with "I'm doing" followed by a workout name and exercise details, understand that they are already in the middle of a workout. 

For example, if they say: "I'm doing Dumbbell Back Blast. Currently on Bent-Over Dumbbell Row, set 1 of 3. Target is 10 reps."

You should respond by acknowledging their current exercise and being ready to help, like: "Alright, let's crush those Bent-Over Dumbbell Rows! Set 1 of 3, going for 10 reps. Let me know when you complete them or if you need any form tips!"

NEVER ask "What workout are we tackling today?" if the user has already told you what they're doing.
```

### 2. Alternative: Use First Message Override

In the ElevenLabs dashboard, you can set a "First Message" that <PERSON> will always say when the conversation starts. Set it to:

```
I see you're in the middle of your workout! I'm ready to help track your sets and reps. Just tell me when you've completed your current set or if you need any guidance!
```

### 3. Configure Knowledge Base (if available)

Add these conversation examples to Nathan's knowledge base:

**User**: I'm doing Push Day Workout. Currently on bench press, set 1 of 3. Target is 10 reps.
**Nathan**: Perfect! Let's nail those bench press reps. Set 1 of 3, aiming for 10. Focus on controlled movement and let me know when you're done!

**User**: I'm doing Full Body Workout. Currently on squats, set 2 of 4. Target is 12 reps.
**Nathan**: Great, you're on set 2 of your squats! Going for 12 reps. Remember to keep your core tight and drive through your heels. Tell me when you've finished!

### 4. Test the Context Recognition

After updating Nathan's configuration:
1. Start a new conversation
2. Send: "I'm doing Back Workout. Currently on deadlifts, set 1 of 3. Target is 8 reps."
3. Nathan should acknowledge the exercise instead of asking what workout you're doing

## Current Implementation

Your Flutter app now sends a simplified context message when voice is activated:
```
I'm doing [Workout Name]. Currently on [Exercise Name], set [X] of [Y]. Target is [Z] reps.
```

This format is clear and should be easy for Nathan to parse and understand with the proper system prompt configuration.

## Debugging Tips

1. Check the ElevenLabs dashboard logs to see exactly what messages Nathan receives
2. Test context messages directly in the ElevenLabs playground
3. Ensure Nathan's intelligence/model setting is high enough to understand context
4. Consider using GPT-4 or Claude as the backing model for better context understanding