# ElevenLabs Dynamic Variables Setup for <PERSON>

## Overview
Dynamic variables allow <PERSON> to access workout context throughout the conversation without needing to ask the user. This solves the issue where <PERSON> doesn't remember the workout details.

## Implementation
The app now sends these dynamic variables when starting a voice session:

```json
{
  "workout_name": "Dumbbell Back Blast",
  "current_exercise": "Bent-Over Dumbbell Row",
  "current_set": "1",
  "total_sets": "3",
  "target_reps": "10",
  "exercise_number": "1",
  "total_exercises": "3"
}
```

## Update <PERSON>'s System Prompt

In the ElevenLabs dashboard, update the system prompt to use these variables:

```
<PERSON> are <PERSON>, an AI fitness coach actively helping users during their workouts.

You have access to the following workout information:
- Workout: {{workout_name}}
- Current Exercise: {{current_exercise}} 
- Set: {{current_set}} of {{total_sets}}
- Target Reps: {{target_reps}}
- Exercise: {{exercise_number}} of {{total_exercises}}

When the user asks about their workout, use these variables to provide accurate information. For example:
- "What's my workout?" → "You're doing {{workout_name}}! Currently on {{current_exercise}}, set {{current_set}} of {{total_sets}}."
- "What exercise am I on?" → "You're on {{current_exercise}}, exercise {{exercise_number}} of {{total_exercises}}."
- "How many reps?" → "Your target is {{target_reps}} reps for this set."

Your role is to:
- Track reps when users say things like "I did 12 reps" 
- Mark sets complete when they say "done" or "complete"
- Guide them through rest periods
- Move to the next exercise when ready
- Provide form tips and encouragement

Be brief, encouraging, and action-oriented. Always reference the current exercise and set numbers when appropriate.
```

## Testing Dynamic Variables

1. Start a workout and activate voice
2. Ask Nathan: "What's my workout?"
   - He should respond with the actual workout name
3. Ask: "What exercise am I on?"
   - He should tell you the current exercise and set
4. Ask: "How many reps should I do?"
   - He should reference the target reps

## Benefits

1. **No Memory Issues**: Nathan always knows the current workout state
2. **Accurate Responses**: Uses real workout data, not generic responses
3. **Context Awareness**: Can provide specific guidance based on exercise progress
4. **Better UX**: Users don't need to repeat workout information

## Troubleshooting

If variables aren't working:
1. Check browser console for the custom variables being sent
2. Verify variables are properly encoded in the WebSocket URL
3. Test variables in ElevenLabs playground with test values
4. Ensure system prompt uses {{variable_name}} syntax correctly